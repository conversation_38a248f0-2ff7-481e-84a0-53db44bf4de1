// Browser debug script for runtime testing
// Copy and paste this into the browser console on http://localhost:3001/admin/pos

console.log('🔍 BROWSER RUNTIME DEBUG STARTING...');

// Check for immediate JavaScript errors
const originalError = console.error;
const originalWarn = console.warn;
let foundErrors = [];
let foundWarnings = [];

console.error = function(...args) {
  foundErrors.push(args.join(' '));
  originalError.apply(console, args);
};

console.warn = function(...args) {
  foundWarnings.push(args.join(' '));
  originalWarn.apply(console, args);
};

// Check React/Next.js state
setTimeout(() => {
  console.log('🔍 Next.js Check:');
  if (typeof window !== 'undefined' && window.__NEXT_DATA__) {
    console.log('✅ Next.js loaded successfully');
    console.log('Build ID:', window.__NEXT_DATA__.buildId);
  } else {
    console.log('❌ Next.js not loaded properly');
  }
}, 500);

// Check authentication state
setTimeout(() => {
  console.log('🔍 Authentication Check:');
  if (typeof window !== 'undefined' && window.supabase) {
    console.log('✅ Supabase client available');
    window.supabase.auth.getSession().then(({ data: { session }, error }) => {
      if (error) {
        console.log('❌ Auth error:', error);
      } else if (session) {
        console.log('✅ User authenticated:', session.user.email);
      } else {
        console.log('⚠️ No active session - user needs to login');
      }
    });
  } else {
    console.log('❌ Supabase client not available');
  }
}, 1000);

// Check for POSSquarePayment component
setTimeout(() => {
  console.log('🔍 POS Square Payment Check:');
  
  // Look for Square container
  const squareContainer = document.querySelector('#pos-square-card-container, [id*="square-card"], [data-square-container]');
  if (squareContainer) {
    console.log('✅ Square container found:', squareContainer.id || squareContainer.className);
    console.log('Container dimensions:', squareContainer.getBoundingClientRect());
  } else {
    console.log('❌ Square container not found');
  }
  
  // Check for Square SDK
  if (typeof window !== 'undefined' && window.Square) {
    console.log('✅ Square SDK loaded');
    console.log('Square version:', window.Square.version || 'unknown');
  } else {
    console.log('⚠️ Square SDK not loaded');
  }
  
  // Check for React components
  const reactElements = document.querySelectorAll('[data-reactroot], [data-reactroot] *');
  console.log(`✅ Found ${reactElements.length} React elements`);
  
  // Check for console monitoring
  if (typeof window !== 'undefined' && window.getConsoleErrors) {
    const consoleData = window.getConsoleErrors();
    console.log('✅ Console monitoring active');
    console.log('Console stats:', {
      errors: consoleData.errorsCount,
      warnings: consoleData.warningsCount,
      logs: consoleData.logsCount
    });
  } else {
    console.log('⚠️ Console monitoring not active');
  }
  
}, 2000);

// Summary after 4 seconds
setTimeout(() => {
  console.log('📊 RUNTIME DEBUG SUMMARY:');
  console.log(`Errors found: ${foundErrors.length}`);
  if (foundErrors.length > 0) {
    console.log('❌ Errors:', foundErrors);
  }
  console.log(`Warnings found: ${foundWarnings.length}`);
  if (foundWarnings.length > 0) {
    console.log('⚠️ Warnings:', foundWarnings);
  }
  
  if (foundErrors.length === 0 && foundWarnings.length === 0) {
    console.log('✅ No immediate runtime errors detected');
  }
  
  // Test POS functionality
  console.log('🧪 Testing POS Square Payment functionality...');
  
  // Check if payment form is ready
  const paymentButton = document.querySelector('button[class*="processPayment"], button[class*="payButton"], button:contains("Charge")');
  if (paymentButton) {
    console.log('✅ Payment button found:', paymentButton.textContent);
  } else {
    console.log('⚠️ Payment button not found');
  }
  
  // Check for billing address form
  const billingForm = document.querySelector('[class*="billingAddress"], [class*="addressGrid"]');
  if (billingForm) {
    console.log('✅ Billing address form found');
  } else {
    console.log('⚠️ Billing address form not found (may appear after card form loads)');
  }
  
  // Restore original console methods
  console.error = originalError;
  console.warn = originalWarn;
  
  console.log('🏁 Runtime debug completed. Check above for any issues.');
}, 4000);
  console.log(`Error ${i + 1}:`, el.textContent.trim());
});

// 3. Check for loading states
const loadingElements = document.querySelectorAll('.loading, [class*="loading"], [class*="Loading"]');
console.log('⏳ Loading elements found:', loadingElements.length);
loadingElements.forEach((el, i) => {
  console.log(`Loading ${i + 1}:`, el.textContent.trim());
});

// 4. Check authentication state
setTimeout(() => {
  console.log('🔐 Auth Check:');
  if (window.supabase) {
    window.supabase.auth.getSession().then(({ data: { session }, error }) => {
      if (error) {
        console.log('❌ Supabase auth error:', error.message);
      } else if (session) {
        console.log('✅ User logged in:', session.user.email);
      } else {
        console.log('⚠️ No user session - redirecting to login expected');
      }
    }).catch(err => {
      console.log('❌ Auth check failed:', err.message);
    });
  } else {
    console.log('❌ Supabase not available');
  }
}, 1000);

// 5. Check React components
setTimeout(() => {
  console.log('⚛️ React Components:');
  const reactRoot = document.querySelector('[data-reactroot], #__next');
  if (reactRoot) {
    console.log('✅ React root found');
    console.log('📦 Children count:', reactRoot.children.length);
    if (reactRoot.children.length === 0) {
      console.log('⚠️ React root is empty - possible rendering issue');
    }
  } else {
    console.log('❌ No React root found');
  }
}, 1500);

// 6. Check network requests
console.log('🌐 Network Status:');
if (navigator.onLine) {
  console.log('✅ Network connected');
} else {
  console.log('❌ Network offline');
}

console.log('📊 Debug complete - check results above');
