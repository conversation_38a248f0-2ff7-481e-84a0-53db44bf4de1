import '@/styles/globals.css'
import '@/styles/admin.css'
import 'react-toastify/dist/ReactToastify.css'
import '@/styles/toastify-performance-fix.css'
import Head from 'next/head'
import React, { useEffect, memo } from 'react'
import { AuthProvider } from '@/contexts/AuthContext'
import { CustomerProvider } from '@/contexts/CustomerContext'
import { ToastContainer } from 'react-toastify'
import ErrorBoundary from '@/components/ErrorBoundary'
import { initializeHydrationFixes } from '@/lib/react-hydration-helpers'
import '@/lib/admin-auth-migration'
import { patchFetch } from '@/lib/auth-fetch'

// Create memoized components to prevent unnecessary re-renders
const MemoizedHead = memo(function AppHead() {
  return (
    <Head>
      <title>OceanSoulSparkles | Melbourne Face Painting & Entertainment</title>

      {/* Essential Meta Tags */}      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta name="description" content="OceanSoulSparkles - Melbourne's premier face painting, airbrush body art, and braiding service for events, festivals, and parties." />
      <meta name="keywords" content="face painting, airbrush body art, braiding, Melbourne, events, festivals, eco-friendly, biodegradable glitter" />
      <link rel="icon" href="/favicon.ico" />
      <link rel="canonical" href="https://wwww.oceansoulsparkles.com.au" />      {/* Open Graph Meta Tags */}
      <meta property="og:title" content="OceanSoulSparkles | Melbourne Face Painting & Entertainment" />
      <meta property="og:description" content="Professional face painting, airbrush body art, and braiding services for events, festivals, and parties in Melbourne." />
      <meta property="og:type" content="website" />
      <meta property="og:url" content="https://wwww.oceansoulsparkles.com.au" />
      <meta property="og:image" content="https://wwww.oceansoulsparkles.com.au/images/og-image.jpg" />
      <meta property="og:site_name" content="OceanSoulSparkles" />
      <meta property="og:locale" content="en_AU" />      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content="OceanSoulSparkles | Melbourne Face Painting & Entertainment" />
      <meta name="twitter:description" content="Professional face painting, airbrush body art, and braiding services for events, festivals, and parties in Melbourne." />
      <meta name="twitter:image" content="https://wwww.oceansoulsparkles.com.au/images/og-image.jpg" />
      <meta name="twitter:site" content="@oceansoulsparkles" />
      <meta name="twitter:creator" content="@oceansoulsparkles" />

      {/* Robots Meta Tag */}
      <meta name="robots" content="index, follow" />

      {/* Additional SEO Meta Tags */}
      <meta name="author" content="OceanSoulSparkles" />
      <meta name="geo.region" content="AU-VIC" />
      <meta name="geo.placename" content="Melbourne" />

      {/* OneSignal initialization is now handled in _document.js */}
    </Head>
  );
});

// Memoized toast container to prevent re-renders and constructor errors
const MemoizedToastContainer = memo(function AppToastContainer() {
  return (
    <ToastContainer
      position="bottom-right"
      autoClose={5000}
      hideProgressBar={false}
      newestOnTop={true}
      closeOnClick={true}
      rtl={false}
      pauseOnFocusLoss={true}
      draggable={true}
      pauseOnHover={true}
      theme="light"
      containerId="main-toast-container"
      enableMultiContainer={false}
      limit={5}
      toastClassName="custom-toast"
      bodyClassName="custom-toast-body"
      progressClassName="custom-toast-progress"
    />
  );
});

function MyApp({ Component, pageProps }) {
  // OneSignal is now initialized in useEffect and via the standalone script

  // Ensure React 18 hydration issues are resolved
  useEffect(() => {
    // Skip on server-side
    if (typeof window === 'undefined') return;

    // 1. Load extension error suppression script first (only if not already loaded)
    if (!document.querySelector('script[src="/scripts/extension-error-suppression.js"]')) {
      const extensionErrorScript = document.createElement('script');
      extensionErrorScript.src = '/scripts/extension-error-suppression.js';
      extensionErrorScript.async = false;
      document.head.appendChild(extensionErrorScript);
      console.log('Loaded extension error suppression script');
    }

    // 2. White screen recovery script removed to prevent conflicts

    // 3. Remove JSS styles to prevent style conflicts during hydration
    const jssStyles = document.getElementById('jss-server-side');
    if (jssStyles) {
      jssStyles.parentElement.removeChild(jssStyles);
    }

    // 4. Apply all hydration fixes from our utility
    initializeHydrationFixes();

    // 5. Initialize auth fetch for authenticated API requests
    patchFetch();
    console.log('Auth fetch initialized');

    // 6. Load fix auth button cleanup script in production
    if (process.env.NODE_ENV === 'production') {
      const cleanupScript = document.createElement('script');
      cleanupScript.src = '/scripts/disable-fix-auth-buttons.js';
      cleanupScript.async = true;
      document.head.appendChild(cleanupScript);
      console.log('Fix auth button cleanup script loaded');
    }

    // 7. OneSignal is now initialized via the standalone script in _document.js
    // and the OneSignalProvider component in Layout.js

    // 8. Add performance debugging in development
    if (process.env.NODE_ENV === 'development') {
      // Track renders to help identify unnecessary re-renders
      const renderCount = {};
      window.__TRACK_RENDER = (componentName) => {
        renderCount[componentName] = (renderCount[componentName] || 0) + 1;
        if (renderCount[componentName] > 5) {
          console.warn(`${componentName} has rendered ${renderCount[componentName]} times`);
        }
      };
    }

    // 9. Client-side error monitoring - send errors to server for logging
    const originalConsoleError = console.error;
    console.error = (...args) => {
      // Call original console.error
      originalConsoleError.apply(console, args);

      // Check for React Error #130 and other critical errors
      const errorMessage = args.join(' ');
      if (errorMessage.includes('Objects are not valid as a React child') ||
          errorMessage.includes('Error #130') ||
          errorMessage.includes('found object with keys') ||
          errorMessage.includes('Element type is invalid')) {

        // Send error to server for logging
        fetch('/api/client-error-log', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            error: errorMessage,
            timestamp: new Date().toISOString(),
            page: window.location.pathname,
            userAgent: navigator.userAgent
          })
        }).catch(() => {}); // Silently fail if logging endpoint doesn't exist
      }
    };

    // Global error handler for unhandled errors
    const handleGlobalError = (event) => {
      const error = event.error || event.reason;
      if (error && error.message) {
        console.error('Global error caught:', error.message, error.stack);
      }
    };

    window.addEventListener('error', handleGlobalError);
    window.addEventListener('unhandledrejection', handleGlobalError);

    return () => {
      console.error = originalConsoleError;
      window.removeEventListener('error', handleGlobalError);
      window.removeEventListener('unhandledrejection', handleGlobalError);
    };
  }, []);

  return (
    <ErrorBoundary>
      <AuthProvider>
        <CustomerProvider>
          <MemoizedHead />

          {/* The main application component */}
          <Component {...pageProps} />

          <MemoizedToastContainer />
        </CustomerProvider>
      </AuthProvider>
    </ErrorBoundary>
  );
}

export default MyApp
