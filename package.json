{"name": "ocean-soul-sparkles-main", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "node scripts/check-env.js && next dev", "build": "node scripts/check-env.js && next build", "build:validate": "node scripts/build-with-validation.js", "build:production": "node scripts/build-with-validation.js --environment=production", "production-check": "node scripts/production-readiness-check.js", "production-check:verbose": "node scripts/production-readiness-check.js --verbose", "start": "next start", "lint": "next lint", "test": "jest --config=jest.config.cjs", "test:watch": "jest --config=jest.config.cjs --watch", "test:api": "jest --config=jest.config.cjs --testPathPattern=__tests__/api", "test:api:watch": "jest --config=jest.config.cjs --testPathPattern=__tests__/api --watch", "test:integration": "jest --config=jest.config.cjs --testPathPattern=__tests__/api-integration", "test:integration:watch": "jest --config=jest.config.cjs --testPathPattern=__tests__/api-integration --watch", "generate-sitemap": "node scripts/generate-sitemap.js", "build:with-sitemap": "npm run generate-sitemap && npm run build", "check-env": "node scripts/check-env.js", "prod-test": "npm run build && npm run start", "test:dev": "cross-env NODE_ENV=development npm run dev", "test:prod": "cross-env NODE_ENV=production npm run prod-test", "test:auth": "jest --config=jest.config.cjs --testPathPattern=__tests__/api/auth", "test:deployment": "powershell -File scripts/test-deployment.ps1", "auth:debug": "cross-env DEBUG=auth node scripts/check-auth.js", "deploy": "node scripts/trigger-deployment.js", "sync-services": "node scripts/sync-services.js", "db:migrate": "node scripts/apply-migration.js", "db:migrate:phase2": "node scripts/apply-migration.js db/migrations/phase2_migration.sql", "security-check": "node scripts/production-security-check.js", "https-scan": "node scripts/https-migration-scanner.js", "https-fix": "node scripts/https-migration-scanner.js --fix", "https-verify": "node scripts/https-verification.js", "build:secure": "npm run security-check && npm run https-scan && npm run build", "deploy:secure": "npm run https-fix && npm run build:secure", "verify:production": "npm run https-verify"}, "dependencies": {"@paypal/react-paypal-js": "^8.1.3", "@square/web-sdk": "^2.0.1", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.49.4", "aos": "^2.3.4", "axios": "^1.9.0", "base64-arraybuffer": "^1.0.2", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "exceljs": "^4.4.0", "formidable": "^3.5.4", "framer-motion": "^12.9.4", "gsap": "^3.13.0", "jsdom": "^26.1.0", "json2csv": "^6.0.0-alpha.2", "moment": "^2.30.1", "next": "^14.0.0", "node-fetch": "^3.3.2", "pdfkit": "^0.17.1", "pg": "^8.16.0", "react": "^18.2.0", "react-big-calendar": "^1.18.0", "react-chartjs-2": "^5.3.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-intersection-observer": "^9.16.0", "react-toastify": "^11.0.5", "readline": "^1.3.0", "uuid": "^9.0.1"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@jest/globals": "^29.7.0", "@svgr/webpack": "^8.1.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "babel-jest": "^29.7.0", "dotenv": "^16.5.0", "eslint": "^8.52.0", "eslint-config-next": "^14.0.0", "glob": "^10.3.10", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-node-exports-resolver": "^1.1.6", "node-mocks-http": "^1.17.2"}}