#!/usr/bin/env node

/**
 * HTTPS Verification Script
 * Verifies that HTTPS is properly configured and working
 */

import https from 'https';
import http from 'http';
import { URL } from 'url';

const DOMAIN = 'www.oceansoulsparkles.com.au';
const TIMEOUT = 10000; // 10 seconds

console.log('🔒 HTTPS Verification for Ocean Soul Sparkles\n');

let testResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  tests: []
};

/**
 * Log test result
 */
function logResult(test, status, message, details = null) {
  const symbols = { pass: '✅', fail: '❌', warn: '⚠️' };
  console.log(`${symbols[status]} ${test}: ${message}`);
  
  if (details) {
    console.log(`   ${details}`);
  }
  
  testResults.tests.push({ test, status, message, details });
  testResults[status === 'pass' ? 'passed' : status === 'fail' ? 'failed' : 'warnings']++;
}

/**
 * Make HTTPS request
 */
function makeHttpsRequest(url) {
  return new Promise((resolve, reject) => {
    const request = https.get(url, { timeout: TIMEOUT }, (response) => {
      let data = '';
      
      response.on('data', (chunk) => {
        data += chunk;
      });
      
      response.on('end', () => {
        resolve({
          statusCode: response.statusCode,
          headers: response.headers,
          data: data,
          url: response.url
        });
      });
    });
    
    request.on('error', reject);
    request.on('timeout', () => {
      request.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

/**
 * Make HTTP request
 */
function makeHttpRequest(url) {
  return new Promise((resolve, reject) => {
    const request = http.get(url, { timeout: TIMEOUT }, (response) => {
      resolve({
        statusCode: response.statusCode,
        headers: response.headers,
        location: response.headers.location
      });
    });
    
    request.on('error', reject);
    request.on('timeout', () => {
      request.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

/**
 * Test HTTPS accessibility
 */
async function testHttpsAccessibility() {
  console.log('🌐 Testing HTTPS Accessibility...\n');
  
  try {
    const response = await makeHttpsRequest(`https://${DOMAIN}`);
    
    if (response.statusCode === 200) {
      logResult('HTTPS Access', 'pass', 'Website accessible via HTTPS');
    } else {
      logResult('HTTPS Access', 'fail', `Unexpected status code: ${response.statusCode}`);
    }
    
    // Check for mixed content indicators
    if (response.data.includes('http://')) {
      logResult('Mixed Content Check', 'warn', 'Potential mixed content found in HTML');
    } else {
      logResult('Mixed Content Check', 'pass', 'No obvious mixed content in HTML');
    }
    
  } catch (error) {
    logResult('HTTPS Access', 'fail', `HTTPS request failed: ${error.message}`);
  }
}

/**
 * Test HTTP to HTTPS redirect
 */
async function testHttpRedirect() {
  console.log('\n🔄 Testing HTTP to HTTPS Redirect...\n');
  
  try {
    const response = await makeHttpRequest(`http://${DOMAIN}`);
    
    if (response.statusCode === 301 || response.statusCode === 302) {
      const location = response.location;
      
      if (location && location.startsWith('https://')) {
        logResult('HTTP Redirect', 'pass', `HTTP redirects to HTTPS (${response.statusCode})`);
        logResult('Redirect Target', 'pass', `Redirects to: ${location}`);
      } else {
        logResult('HTTP Redirect', 'fail', `Redirects but not to HTTPS: ${location}`);
      }
    } else {
      logResult('HTTP Redirect', 'fail', `No redirect found (status: ${response.statusCode})`);
    }
    
  } catch (error) {
    logResult('HTTP Redirect', 'fail', `HTTP request failed: ${error.message}`);
  }
}

/**
 * Test security headers
 */
async function testSecurityHeaders() {
  console.log('\n🛡️ Testing Security Headers...\n');
  
  try {
    const response = await makeHttpsRequest(`https://${DOMAIN}`);
    const headers = response.headers;
    
    // Required security headers
    const securityHeaders = [
      { name: 'strict-transport-security', description: 'HSTS' },
      { name: 'x-frame-options', description: 'X-Frame-Options' },
      { name: 'x-content-type-options', description: 'X-Content-Type-Options' },
      { name: 'referrer-policy', description: 'Referrer-Policy' },
      { name: 'content-security-policy', description: 'Content-Security-Policy' }
    ];
    
    securityHeaders.forEach(({ name, description }) => {
      if (headers[name]) {
        logResult(`${description} Header`, 'pass', `Present: ${headers[name]}`);
      } else {
        logResult(`${description} Header`, 'warn', 'Missing security header');
      }
    });
    
  } catch (error) {
    logResult('Security Headers', 'fail', `Failed to check headers: ${error.message}`);
  }
}

/**
 * Test SSL certificate
 */
async function testSSLCertificate() {
  console.log('\n🔐 Testing SSL Certificate...\n');
  
  return new Promise((resolve) => {
    const options = {
      hostname: DOMAIN,
      port: 443,
      method: 'GET',
      path: '/',
      timeout: TIMEOUT
    };
    
    const request = https.request(options, (response) => {
      const cert = response.connection.getPeerCertificate();
      
      if (cert && Object.keys(cert).length > 0) {
        logResult('SSL Certificate', 'pass', 'Valid SSL certificate found');
        
        // Check certificate details
        if (cert.subject && cert.subject.CN) {
          logResult('Certificate Subject', 'pass', `CN: ${cert.subject.CN}`);
        }
        
        if (cert.issuer && cert.issuer.O) {
          logResult('Certificate Issuer', 'pass', `Issuer: ${cert.issuer.O}`);
        }
        
        if (cert.valid_to) {
          const expiryDate = new Date(cert.valid_to);
          const now = new Date();
          const daysUntilExpiry = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));
          
          if (daysUntilExpiry > 30) {
            logResult('Certificate Expiry', 'pass', `Expires in ${daysUntilExpiry} days`);
          } else if (daysUntilExpiry > 0) {
            logResult('Certificate Expiry', 'warn', `Expires in ${daysUntilExpiry} days`);
          } else {
            logResult('Certificate Expiry', 'fail', 'Certificate has expired');
          }
        }
      } else {
        logResult('SSL Certificate', 'fail', 'No SSL certificate found');
      }
      
      resolve();
    });
    
    request.on('error', (error) => {
      logResult('SSL Certificate', 'fail', `SSL test failed: ${error.message}`);
      resolve();
    });
    
    request.on('timeout', () => {
      request.destroy();
      logResult('SSL Certificate', 'fail', 'SSL test timeout');
      resolve();
    });
    
    request.end();
  });
}

/**
 * Test admin panel HTTPS
 */
async function testAdminPanelHttps() {
  console.log('\n👤 Testing Admin Panel HTTPS...\n');
  
  try {
    const response = await makeHttpsRequest(`https://${DOMAIN}/admin`);
    
    if (response.statusCode === 200 || response.statusCode === 302 || response.statusCode === 401) {
      logResult('Admin Panel HTTPS', 'pass', 'Admin panel accessible via HTTPS');
    } else {
      logResult('Admin Panel HTTPS', 'warn', `Unexpected status: ${response.statusCode}`);
    }
    
  } catch (error) {
    logResult('Admin Panel HTTPS', 'fail', `Admin panel HTTPS test failed: ${error.message}`);
  }
}

/**
 * Test API endpoints HTTPS
 */
async function testApiHttps() {
  console.log('\n🔌 Testing API HTTPS...\n');
  
  const apiEndpoints = [
    '/api/health',
    '/api/admin/health'
  ];
  
  for (const endpoint of apiEndpoints) {
    try {
      const response = await makeHttpsRequest(`https://${DOMAIN}${endpoint}`);
      
      if (response.statusCode < 500) {
        logResult(`API ${endpoint}`, 'pass', `Accessible via HTTPS (${response.statusCode})`);
      } else {
        logResult(`API ${endpoint}`, 'warn', `Server error: ${response.statusCode}`);
      }
      
    } catch (error) {
      logResult(`API ${endpoint}`, 'fail', `API test failed: ${error.message}`);
    }
  }
}

/**
 * Generate final report
 */
function generateReport() {
  console.log('\n📊 HTTPS Verification Report\n');
  console.log(`Tests run: ${testResults.tests.length}`);
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`⚠️  Warnings: ${testResults.warnings}`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ HTTPS VERIFICATION FAILED');
    console.log('Please fix the failed tests before going live with HTTPS.\n');
    
    console.log('Failed tests:');
    testResults.tests
      .filter(test => test.status === 'fail')
      .forEach(test => console.log(`  - ${test.test}: ${test.message}`));
    
    return false;
  } else if (testResults.warnings > 0) {
    console.log('\n⚠️  HTTPS VERIFICATION PASSED WITH WARNINGS');
    console.log('Consider addressing the warnings for better security.\n');
    
    console.log('Warnings:');
    testResults.tests
      .filter(test => test.status === 'warn')
      .forEach(test => console.log(`  - ${test.test}: ${test.message}`));
    
    return true;
  } else {
    console.log('\n✅ HTTPS VERIFICATION PASSED');
    console.log('Your website is properly configured for HTTPS!\n');
    return true;
  }
}

/**
 * Main execution
 */
async function main() {
  console.log(`Testing domain: ${DOMAIN}\n`);
  
  await testHttpsAccessibility();
  await testHttpRedirect();
  await testSecurityHeaders();
  await testSSLCertificate();
  await testAdminPanelHttps();
  await testApiHttps();
  
  const success = generateReport();
  
  if (success) {
    console.log('🎉 Your website is ready for secure HTTPS operation!');
    console.log('\nNext steps:');
    console.log('1. Update any external service webhooks to use HTTPS URLs');
    console.log('2. Submit HTTPS sitemap to Google Search Console');
    console.log('3. Update social media profiles with HTTPS URLs');
    console.log('4. Monitor for any mixed content warnings');
  } else {
    console.log('🔧 Please fix the issues above and run the test again.');
  }
  
  process.exit(success ? 0 : 1);
}

// Run the verification
main().catch(error => {
  console.error('❌ Verification failed:', error);
  process.exit(1);
});
