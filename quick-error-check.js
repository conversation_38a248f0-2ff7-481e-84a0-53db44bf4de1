// Quick error detection script for browser console
// Copy and paste this into the browser console on the /admin/pos page

console.log('🔍 QUICK ERROR DETECTION STARTING...');

// Check for immediate JavaScript errors
const originalError = console.error;
const originalWarn = console.warn;
let foundErrors = [];
let foundWarnings = [];

console.error = function(...args) {
  foundErrors.push(args.join(' '));
  originalError.apply(console, args);
};

console.warn = function(...args) {
  foundWarnings.push(args.join(' '));
  originalWarn.apply(console, args);
};

// Check React/Next.js errors
if (window.__NEXT_DATA__) {
  console.log('✅ Next.js loaded successfully');
} else {
  console.log('❌ Next.js not loaded properly');
}

// Check authentication state
setTimeout(() => {
  console.log('🔍 Authentication Check:');
  if (window.supabase) {
    console.log('✅ Supabase client available');
    window.supabase.auth.getSession().then(({ data: { session }, error }) => {
      if (error) {
        console.log('❌ Auth error:', error);
      } else if (session) {
        console.log('✅ User authenticated:', session.user.email);
      } else {
        console.log('⚠️ No active session - user needs to login');
      }
    });
  } else {
    console.log('❌ Supabase client not available');
  }
}, 1000);

// Check for POSSquarePayment component errors
setTimeout(() => {
  console.log('🔍 POS Component Check:');
  
  // Look for POSSquarePayment related elements
  const posContainers = document.querySelectorAll('[class*="pos"], [id*="pos"], [data-testid*="square"]');
  console.log(`Found ${posContainers.length} POS-related elements`);
  
  // Check for Square SDK
  if (window.Square) {
    console.log('✅ Square SDK loaded');
  } else {
    console.log('⚠️ Square SDK not loaded');
  }
  
  // Check for React errors in the page
  const reactErrors = document.querySelectorAll('[data-reactroot] *').length;
  if (reactErrors > 0) {
    console.log('✅ React components rendered');
  } else {
    console.log('❌ No React components found');
  }
  
}, 2000);

// Summary after 3 seconds
setTimeout(() => {
  console.log('📊 ERROR DETECTION SUMMARY:');
  console.log(`Errors found: ${foundErrors.length}`);
  if (foundErrors.length > 0) {
    console.log('❌ Errors:', foundErrors);
  }
  console.log(`Warnings found: ${foundWarnings.length}`);
  if (foundWarnings.length > 0) {
    console.log('⚠️ Warnings:', foundWarnings);
  }
  
  if (foundErrors.length === 0 && foundWarnings.length === 0) {
    console.log('✅ No immediate errors detected');
  }
  
  // Restore original console methods
  console.error = originalError;
  console.warn = originalWarn;
}, 3000);

console.log('⏳ Error detection running... results in 3 seconds');
