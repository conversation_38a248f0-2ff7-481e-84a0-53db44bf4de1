/**
 * Global Console Error Filter
 * Filters out browser extension errors from console output
 */

let originalConsoleError = null
let originalConsoleWarn = null
let originalConsoleLog = null
let isFilterActive = false

// Comprehensive patterns for browser extension errors
const EXTENSION_ERROR_PATTERNS = [
  'runtime.lastError',
  'message port closed',
  'extension context invalidated',
  'chrome-extension://',
  'moz-extension://',
  'safari-extension://',
  'could not establish connection',
  'receiving end does not exist',
  'extension context',
  'chrome.runtime',
  'browser.runtime',
  'unchecked runtime',
  'the message port closed before a response was received',
  'cannot access chrome://',
  'cannot access about:',
  'script error',
  'non-error promise rejection captured',
  'loading css chunk',
  'loading chunk',
  'dynamically imported module'
]

/**
 * Check if a message matches extension error patterns
 */
function isExtensionError(message) {
  const messageStr = String(message).toLowerCase()
  return EXTENSION_ERROR_PATTERNS.some(pattern => 
    messageStr.includes(pattern.toLowerCase())
  )
}

/**
 * Create filtered console method
 */
function createFilteredConsoleMethod(originalMethod, methodName) {
  return (...args) => {
    // Convert all arguments to string for pattern matching
    const message = args.map(arg => {
      if (typeof arg === 'object') {
        try {
          return JSON.stringify(arg)
        } catch {
          return String(arg)
        }
      }
      return String(arg)
    }).join(' ')

    // Check if this is an extension error
    if (isExtensionError(message)) {
      // Completely suppress extension errors
      return
    }

    // Log non-extension errors normally
    originalMethod.apply(console, args)
  }
}

/**
 * Activate global console filtering
 */
export function activateConsoleFilter() {
  if (isFilterActive) return

  // Store original methods
  originalConsoleError = console.error
  originalConsoleWarn = console.warn
  originalConsoleLog = console.log

  // Override console methods
  console.error = createFilteredConsoleMethod(originalConsoleError, 'error')
  console.warn = createFilteredConsoleMethod(originalConsoleWarn, 'warn')
  console.log = createFilteredConsoleMethod(originalConsoleLog, 'log')

  isFilterActive = true
  console.info('🔇 Console filter activated - browser extension errors will be suppressed')
}

/**
 * Deactivate global console filtering
 */
export function deactivateConsoleFilter() {
  if (!isFilterActive) return

  // Restore original methods
  if (originalConsoleError) console.error = originalConsoleError
  if (originalConsoleWarn) console.warn = originalConsoleWarn
  if (originalConsoleLog) console.log = originalConsoleLog

  isFilterActive = false
  console.info('🔊 Console filter deactivated')
}

/**
 * Auto-activate filter in development mode
 */
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // Activate filter after a short delay to ensure console is ready
  setTimeout(activateConsoleFilter, 100)
}
