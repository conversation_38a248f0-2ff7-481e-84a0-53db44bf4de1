/**
 * Production Security Configuration
 * Implements security measures for production deployment
 */

import { disableProductionConsole } from './security-utils';

/**
 * Initialize production security measures
 */
export function initializeProductionSecurity() {
  if (typeof window === 'undefined') return; // Server-side only

  const isProduction = process.env.NODE_ENV === 'production';
  const isDevMode = process.env.NEXT_PUBLIC_DEV_MODE === 'true';

  if (isProduction && !isDevMode) {
    // Disable console logging for security
    disableProductionConsole();

    // Remove development tools from window object
    removeDevTools();

    // Add security headers via meta tags
    addSecurityHeaders();

    // Disable right-click context menu on sensitive areas
    disableContextMenuOnSensitiveAreas();

    // Add basic protection against common attacks
    addBasicProtections();
  }
}

/**
 * Remove development tools and debugging utilities
 */
function removeDevTools() {
  try {
    // Remove common debugging tools
    delete window.__REACT_DEVTOOLS_GLOBAL_HOOK__;
    delete window.__REDUX_DEVTOOLS_EXTENSION__;
    delete window.__VUE_DEVTOOLS_GLOBAL_HOOK__;
    
    // Remove console monitoring tools
    delete window.consoleErrors;
    delete window.consoleWarnings;
    delete window.consoleLogs;
    delete window.getConsoleErrors;
    
    // Remove auth debugging tools
    delete window.authDebug;
    delete window.testAuth;
    delete window.debugAuth;
    
    // Remove any exposed admin functions
    delete window.adminDebug;
    delete window.adminTest;
  } catch (error) {
    // Silently fail - don't expose errors
  }
}

/**
 * Add security headers via meta tags
 */
function addSecurityHeaders() {
  try {
    const head = document.head;
    
    // Content Security Policy
    const csp = document.createElement('meta');
    csp.httpEquiv = 'Content-Security-Policy';
    csp.content = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.squareup.com https://connect.squareup.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https://ndlgbcsbidyhxbpqzgqp.supabase.co https://connect.squareup.com;";
    head.appendChild(csp);
    
    // X-Frame-Options
    const frameOptions = document.createElement('meta');
    frameOptions.httpEquiv = 'X-Frame-Options';
    frameOptions.content = 'DENY';
    head.appendChild(frameOptions);
    
    // X-Content-Type-Options
    const contentType = document.createElement('meta');
    contentType.httpEquiv = 'X-Content-Type-Options';
    contentType.content = 'nosniff';
    head.appendChild(contentType);
    
    // Referrer Policy
    const referrer = document.createElement('meta');
    referrer.name = 'referrer';
    referrer.content = 'strict-origin-when-cross-origin';
    head.appendChild(referrer);
  } catch (error) {
    // Silently fail
  }
}

/**
 * Disable context menu on sensitive areas
 */
function disableContextMenuOnSensitiveAreas() {
  try {
    // Disable right-click on admin areas
    document.addEventListener('contextmenu', (e) => {
      const target = e.target;
      const isAdminArea = target.closest('[data-admin]') || 
                         target.closest('.admin-panel') ||
                         window.location.pathname.startsWith('/admin');
      
      if (isAdminArea) {
        e.preventDefault();
        return false;
      }
    });
    
    // Disable F12, Ctrl+Shift+I, Ctrl+U on admin pages
    document.addEventListener('keydown', (e) => {
      const isAdminPage = window.location.pathname.startsWith('/admin');
      
      if (isAdminPage) {
        // F12
        if (e.keyCode === 123) {
          e.preventDefault();
          return false;
        }
        
        // Ctrl+Shift+I
        if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {
          e.preventDefault();
          return false;
        }
        
        // Ctrl+U
        if (e.ctrlKey && e.keyCode === 85) {
          e.preventDefault();
          return false;
        }
      }
    });
  } catch (error) {
    // Silently fail
  }
}

/**
 * Add basic protections against common attacks
 */
function addBasicProtections() {
  try {
    // Detect and prevent basic XSS attempts
    const originalInnerHTML = Element.prototype.innerHTML;
    Object.defineProperty(Element.prototype, 'innerHTML', {
      set: function(value) {
        if (typeof value === 'string' && value.includes('<script')) {
          console.warn('Potential XSS attempt blocked');
          return;
        }
        return originalInnerHTML.call(this, value);
      },
      get: function() {
        return originalInnerHTML.call(this);
      }
    });
    
    // Monitor for suspicious activity
    let suspiciousActivity = 0;
    const maxSuspiciousActivity = 5;
    
    // Monitor rapid API calls
    const apiCallTimes = [];
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
      const now = Date.now();
      apiCallTimes.push(now);
      
      // Remove calls older than 1 minute
      while (apiCallTimes.length > 0 && now - apiCallTimes[0] > 60000) {
        apiCallTimes.shift();
      }
      
      // Check for rapid API calls (more than 20 in 1 minute)
      if (apiCallTimes.length > 20) {
        suspiciousActivity++;
        if (suspiciousActivity > maxSuspiciousActivity) {
          // Block further API calls for this session
          return Promise.reject(new Error('Rate limit exceeded'));
        }
      }
      
      return originalFetch.apply(this, args);
    };
    
  } catch (error) {
    // Silently fail
  }
}

/**
 * Check if current environment is secure
 */
export function isSecureEnvironment() {
  const isProduction = process.env.NODE_ENV === 'production';
  const isHTTPS = typeof window !== 'undefined' && window.location.protocol === 'https:';
  const isLocalhost = typeof window !== 'undefined' && window.location.hostname === 'localhost';
  
  return isProduction && (isHTTPS || isLocalhost);
}

/**
 * Validate admin access in production
 */
export function validateAdminAccess(user, role) {
  const isProduction = process.env.NODE_ENV === 'production';
  const isDevMode = process.env.NEXT_PUBLIC_DEV_MODE === 'true';
  
  if (!isProduction) {
    return true; // Allow in development
  }
  
  if (isDevMode) {
    return ['admin', 'staff'].includes(role); // Allow with proper role in dev mode
  }
  
  // In production, be very strict
  const allowedAdmins = [
    '<EMAIL>',
    '<EMAIL>'
  ];
  
  return allowedAdmins.includes(user?.email) && role === 'admin';
}

/**
 * Sanitize URLs to prevent admin panel discovery
 */
export function sanitizeURL(url) {
  if (typeof window === 'undefined') return url;
  
  const isProduction = process.env.NODE_ENV === 'production';
  const isDevMode = process.env.NEXT_PUBLIC_DEV_MODE === 'true';
  
  if (isProduction && !isDevMode) {
    // Remove admin paths from browser history in production
    if (url.includes('/admin') && !validateCurrentUserAccess()) {
      return '/';
    }
  }
  
  return url;
}

/**
 * Validate current user has admin access
 */
function validateCurrentUserAccess() {
  try {
    // This would need to be implemented based on your auth system
    // For now, return false to be safe
    return false;
  } catch (error) {
    return false;
  }
}

/**
 * Initialize security on page load
 */
if (typeof window !== 'undefined') {
  document.addEventListener('DOMContentLoaded', initializeProductionSecurity);
  
  // Also initialize immediately if DOM is already loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeProductionSecurity);
  } else {
    initializeProductionSecurity();
  }
}
