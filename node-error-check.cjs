#!/usr/bin/env node
// Node.js error detection script for static code analysis

const fs = require('fs');
const path = require('path');

console.log('🔍 NODE.JS ERROR DETECTION STARTING...');

// Check for common error patterns in key files
const filesToCheck = [
  'components/admin/pos/POSSquarePayment.js',
  'lib/supabase.js',
  'public/console-monitor.js',
  '.env.local'
];

let foundIssues = [];

// Function to check file for common issues
function checkFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      foundIssues.push(`❌ File missing: ${filePath}`);
      return;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    console.log(`🔍 Checking ${filePath}...`);
    
    // Check for common error patterns
    lines.forEach((line, index) => {
      const lineNum = index + 1;
      
      // Check for window access without SSR check
      if (line.includes('window.') && !line.includes('typeof window') && !line.includes('// SSR safe')) {
        foundIssues.push(`⚠️ ${filePath}:${lineNum} - Potential SSR issue: ${line.trim()}`);
      }
      
      // Check for missing error handling in async functions
      if (line.includes('await ') && !content.includes('try') && !content.includes('catch')) {
        foundIssues.push(`⚠️ ${filePath}:${lineNum} - Async without error handling: ${line.trim()}`);
      }
      
      // Check for console.log in production code
      if (line.includes('console.log') && !filePath.includes('console-monitor')) {
        foundIssues.push(`⚠️ ${filePath}:${lineNum} - Console.log found: ${line.trim()}`);
      }
      
      // Check for hardcoded localhost URLs
      if (line.includes('localhost:3000') && !filePath.includes('.env')) {
        foundIssues.push(`⚠️ ${filePath}:${lineNum} - Hardcoded localhost:3000: ${line.trim()}`);
      }
    });
    
    console.log(`✅ ${filePath} checked`);
    
  } catch (error) {
    foundIssues.push(`❌ Error reading ${filePath}: ${error.message}`);
  }
}

// Check each file
filesToCheck.forEach(checkFile);

// Check for specific POSSquarePayment issues
console.log('\n🔍 Checking POSSquarePayment specific issues...');

const posFile = 'components/admin/pos/POSSquarePayment.js';
if (fs.existsSync(posFile)) {
  const posContent = fs.readFileSync(posFile, 'utf8');
  
  // Check for required fixes
  const requiredPatterns = [
    { pattern: 'typeof window !== \'undefined\'', description: 'SSR check' },
    { pattern: 'paymentFormRef', description: 'Ref-based approach' },
    { pattern: 'data-square-container', description: 'Container tracking' },
    { pattern: 'trimArray', description: 'Memory management' }
  ];
  
  requiredPatterns.forEach(({ pattern, description }) => {
    if (!posContent.includes(pattern)) {
      foundIssues.push(`❌ POSSquarePayment missing ${description}: ${pattern}`);
    } else {
      console.log(`✅ POSSquarePayment has ${description}`);
    }
  });
}

// Check environment configuration
console.log('\n🔍 Checking environment configuration...');
const envFile = '.env.local';
if (fs.existsSync(envFile)) {
  const envContent = fs.readFileSync(envFile, 'utf8');
  
  if (envContent.includes('localhost:3000')) {
    foundIssues.push('❌ .env.local still contains localhost:3000 - should be 3002');
  } else if (envContent.includes('localhost:3002')) {
    console.log('✅ .env.local correctly configured for port 3002');
  }
}

// Summary
console.log('\n📊 ERROR DETECTION SUMMARY:');
console.log(`Issues found: ${foundIssues.length}`);

if (foundIssues.length > 0) {
  console.log('\n❌ ISSUES FOUND:');
  foundIssues.forEach(issue => console.log(issue));
} else {
  console.log('✅ No major issues detected in static analysis');
}

console.log('\n💡 To check runtime errors, use the browser-debug.js script in the browser console');
console.log('💡 Visit http://localhost:3002/admin/pos and paste browser-debug.js content');
