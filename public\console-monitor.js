// Console error monitoring for debugging
(function() {
  const originalConsoleError = console.error;
  const originalConsoleWarn = console.warn;
  const originalConsoleLog = console.log;

  // Configuration for array size limits
  const MAX_ENTRIES = 100; // Keep only the last 100 entries per array

  // Store errors for debugging
  window.consoleErrors = [];
  window.consoleWarnings = [];
  window.consoleLogs = [];

  // Function to trim arrays to prevent memory leaks
  function trimArray(array, maxSize = MAX_ENTRIES) {
    if (array.length > maxSize) {
      array.splice(0, array.length - maxSize);
    }
  }

  console.error = function(...args) {
    window.consoleErrors.push({
      timestamp: new Date().toISOString(),
      message: args.join(' '),
      stack: new Error().stack
    });
    trimArray(window.consoleErrors);
    originalConsoleError.apply(console, args);
  };

  console.warn = function(...args) {
    window.consoleWarnings.push({
      timestamp: new Date().toISOString(),
      message: args.join(' ')
    });
    trimArray(window.consoleWarnings);
    originalConsoleWarn.apply(console, args);
  };

  console.log = function(...args) {
    window.consoleLogs.push({
      timestamp: new Date().toISOString(),
      message: args.join(' ')
    });
    trimArray(window.consoleLogs);
    originalConsoleLog.apply(console, args);
  };

  // Global error handler
  window.addEventListener('error', function(event) {
    window.consoleErrors.push({
      timestamp: new Date().toISOString(),
      message: `Global Error: ${event.message}`,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      error: event.error
    });
    trimArray(window.consoleErrors);
  });

  // Unhandled promise rejection handler
  window.addEventListener('unhandledrejection', function(event) {
    window.consoleErrors.push({
      timestamp: new Date().toISOString(),
      message: `Unhandled Promise Rejection: ${event.reason}`,
      type: 'unhandledrejection'
    });
    trimArray(window.consoleErrors);
  });

  // Helper function to get all errors
  window.getConsoleErrors = function() {
    return {
      errors: window.consoleErrors,
      warnings: window.consoleWarnings,
      logs: window.consoleLogs.filter(log => 
        log.message.includes('Square') || 
        log.message.includes('Payment') || 
        log.message.includes('Error') ||
        log.message.includes('🔄') ||
        log.message.includes('✅') ||
        log.message.includes('❌')
      ),
      meta: {
        errorsCount: window.consoleErrors.length,
        warningsCount: window.consoleWarnings.length,
        logsCount: window.consoleLogs.length,
        maxEntries: MAX_ENTRIES
      }
    };
  };

  // Helper function to manually clear all console arrays
  window.clearConsoleArrays = function() {
    window.consoleErrors.length = 0;
    window.consoleWarnings.length = 0;
    window.consoleLogs.length = 0;
    console.log('Console monitoring arrays cleared');
  };

  console.log('Console monitoring initialized');
})();
